local Players = game:GetService("Players")

local SwordManager = require(script.Parent.SwordManager)

local CombatHandler = {}

local POINTS_PERCENTAGE = 0.25
local DataStoreProvider: any = nil

local function isValidTarget(attacker: Player, target: Player): boolean
	if attacker == target then
		return false
	end
	
	if not target.Parent then
		return false
	end
	
	local targetCharacter = target.Character
	local targetHumanoid = targetCharacter and targetCharacter:FindFirstChild("Humanoid")
	
	if not targetHumanoid or targetHumanoid.Health <= 0 then
		return false
	end
	
	return true
end

local function calculatePointsReward(victimPlayer: Player): number
	if not DataStoreProvider then
		return 0
	end

	local victimPoints = DataStoreProvider.getPoints(victimPlayer)
	return math.floor(victimPoints * POINTS_PERCENTAGE)
end

local function killPlayer(victim: Player, attacker: Player)
	if not DataStoreProvider then
		return
	end

	local victimCharacter = victim.Character
	local victimHumanoid = victimCharacter and victimCharacter:FindFirstChild("Humanoid")

	if not victimHumanoid then
		return
	end

	local pointsReward = calculatePointsReward(victim)

	victimHumanoid.Health = 0

	DataStoreProvider.addDeath(victim)
	DataStoreProvider.addKill(attacker)

	if pointsReward > 0 then
		DataStoreProvider.addPoints(attacker, pointsReward)
	end
end

function CombatHandler.handleSwordHit(attacker: Player, hitPart: BasePart, swordName: string)
	if not hitPart or not hitPart.Parent then
		return
	end
	
	local targetCharacter = hitPart.Parent
	local targetHumanoid = targetCharacter:FindFirstChild("Humanoid")
	local targetPlayer = Players:GetPlayerFromCharacter(targetCharacter)
	
	if not targetPlayer or not targetHumanoid then
		return
	end
	
	if not isValidTarget(attacker, targetPlayer) then
		return
	end
	
	local attackerSword = SwordManager.getCurrentSword(attacker)
	if attackerSword ~= swordName then
		return
	end
	
	killPlayer(targetPlayer, attacker)
end

function CombatHandler.setDataStoreProvider(provider: any)
	DataStoreProvider = provider
end

return CombatHandler
