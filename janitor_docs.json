{"functions": [{"name": "new", "desc": "Instantiates a new Janitor object.", "params": [], "returns": [{"desc": "", "lua_type": "<PERSON><PERSON>"}], "function_type": "static", "source": {"line": 162, "path": "src/init.luau"}}, {"name": "Is", "desc": "Determines if the passed object is a Janitor. This checks the metatable\ndirectly.", "params": [{"name": "object", "desc": "The object you are checking.", "lua_type": "unknown"}], "returns": [{"desc": "`true` if `object` is a Janitor.", "lua_type": "boolean"}], "function_type": "static", "source": {"line": 175, "path": "src/init.luau"}}, {"name": "instanceof", "desc": "An alias for [Janitor.Is]. This is intended for roblox-ts support.", "params": [{"name": "object", "desc": "The object you are checking.", "lua_type": "unknown"}], "returns": [{"desc": "`true` if `object` is a Janitor.", "lua_type": "boolean"}], "function_type": "static", "source": {"line": 188, "path": "src/init.luau"}}, {"name": "Add", "desc": "Adds an `object` to <PERSON><PERSON> for later cleanup, where `methodName` is the\nkey of the method within `object` which should be called at cleanup time.\nIf the `methodName` is `true` the `object` itself will be called if it's a\nfunction or have `task.cancel` called on it if it is a thread. If passed an\nindex it will occupy a namespace which can be `Remove()`d or overwritten.\nReturns the `object`.\n\n:::info Note\nObjects not given an explicit `methodName` will be passed into the `typeof`\nfunction for a very naive typecheck. RBXConnections will be assigned to\n\"Disconnect\", functions and threads will be assigned to `true`, and\neverything else will default to \"Destroy\". Not recommended, but hey, you do\nyou.\n:::\n\n### Luau:\n\n```lua\nlocal Workspace = game:GetService(\"Workspace\")\nlocal TweenService = game:GetService(\"TweenService\")\n\nlocal obliterator = Janitor.new()\nlocal part = Workspace:FindFirstChild(\"Part\") :: Part\n\n-- Queue the Part to be Destroyed at Cleanup time\nobliterator:Add(part, \"Destroy\")\n\n-- Queue function to be called with `true` methodName\nobliterator:Add(print, true)\n\n-- Close a thread.\nobliterator:Add(task.defer(function()\n\twhile true do\n\t\tprint(\"Running!\")\n\t\ttask.wait(0.5)\n\tend\nend), true)\n\n-- This implementation allows you to specify behavior for any object\nobliterator:Add(TweenService:Create(part, TweenInfo.new(1), {Size = Vector3.one}), \"Cancel\")\n\n-- By passing an index, the object will occupy a namespace\n-- If \"CurrentTween\" already exists, it will call :Remove(\"CurrentTween\") before writing\nobliterator:Add(TweenService:Create(part, TweenInfo.new(1), {Size = Vector3.one}), \"Destroy\", \"CurrentTween\")\n```\n\n### TypeScript:\n\n```ts\nimport { Workspace, TweenService } from \"@rbxts/services\";\nimport { Janitor } from \"@rbxts/janitor\";\n\nconst obliterator = new Janitor<{ CurrentTween: Tween }>();\nconst part = Workspace.FindFirstChild(\"Part\") as Part;\n\n// Queue the part to be Destroyed at Cleanup time\nobliterator.Add(part, \"Destroy\");\n\n// Queue function to be called with `true` methodName\nobliterator.Add(print, true);\n\n// Close a thread.\nobliterator.Add(task.defer(() => {\n\twhile (true) {\n\t\tprint(\"Running!\");\n\t\ttask.wait(0.5);\n\t}\n}), true);\n\n// This implementation allows you to specify behavior for any object\nobliterator.Add(TweenService.Create(part, new TweenInfo(1), { Size: Vector3.one }), \"Cancel\");\n\n// By passing an index, the object will occupy a namespace\n// If \"CurrentTween\" already exists, it will call :Remove(\"CurrentTween\") before writing\nobliterator.Add(TweenService.Create(part, new TweenInfo(1), { Size: Vector3.one }), \"Destroy\", \"CurrentTween\");\n```", "params": [{"name": "object", "desc": "The object you want to clean up.", "lua_type": "T"}, {"name": "methodName?", "desc": "The name of the method that will be used to clean up. If not passed, it will first check if the object's type exists in TypeDefaults, and if that doesn't exist, it assumes `Destroy`.", "lua_type": "boolean | string"}, {"name": "index?", "desc": "The index that can be used to clean up the object manually.", "lua_type": "unknown"}], "returns": [{"desc": "The object that was passed as the first argument.", "lua_type": "T"}], "function_type": "method", "source": {"line": 388, "path": "src/init.luau"}}, {"name": "AddObject", "desc": "Constructs an object for you and adds it to the Janitor. It's really just\nshorthand for `Janitor:Add(object.new(), methodName, index)`.\n\n### Luau:\n\n```lua\nlocal obliterator = Janitor.new()\nlocal subObliterator = obliterator:AddObject(Janitor, \"Destroy\")\n-- subObliterator is another Janitor!\n```\n\n### TypeScript:\n\n```ts\nimport { Janitor } from \"@rbxts/janitor\";\n\nconst obliterator = new Janitor();\nconst subObliterator = obliterator.AddObject(Janitor, \"Destroy\");\n```", "params": [{"name": "constructor", "desc": "The constructor for the object you want to add to the Janitor.", "lua_type": "{new: (A...) -> T}"}, {"name": "methodName?", "desc": "The name of the method that will be used to clean up. If not passed, it will first check if the object's type exists in TypeDefaults, and if that doesn't exist, it assumes `Destroy`.", "lua_type": "boolean | string"}, {"name": "index?", "desc": "The index that can be used to clean up the object manually.", "lua_type": "unknown"}, {"name": "...", "desc": "The arguments that will be passed to the constructor.", "lua_type": "A..."}], "returns": [{"desc": "The object that was passed as the first argument.", "lua_type": "T"}], "function_type": "method", "since": "v1.16.0", "source": {"line": 418, "path": "src/init.luau"}}, {"name": "Get", "desc": "Gets whatever object is stored with the given index, if it exists. This was\nadded since <PERSON> allows getting the task using `__index`.\n\n### Luau:\n\n```lua\nlocal obliterator = Janitor.new()\nobliterator:Add(Workspace.Baseplate, \"Destroy\", \"Baseplate\")\nprint(obliterator:Get(\"Baseplate\")) -- Returns Baseplate.\n```\n\n### TypeScript:\n\n```ts\nimport { Workspace } from \"@rbxts/services\";\nimport { Janitor } from \"@rbxts/janitor\";\n\nconst obliterator = new Janitor<{ Baseplate: Part }>();\nobliterator.Add(Workspace.FindFirstChild(\"Baseplate\") as Part, \"Destroy\", \"Baseplate\");\nprint(obliterator.Get(\"Baseplate\")); // Returns Baseplate.\n```", "params": [{"name": "index", "desc": "The index that the object is stored under.", "lua_type": "unknown"}], "returns": [{"desc": "This will return the object if it is found, but it won't return anything if it doesn't exist.", "lua_type": "unknown?"}], "function_type": "method", "source": {"line": 456, "path": "src/init.luau"}}, {"name": "AddPromise", "desc": "Adds a [Promise](https://github.com/evaera/roblox-lua-promise) to the\nJan<PERSON>. If the Janitor is cleaned up and the Promise is not completed, the\nPromise will be cancelled.\n\n### Luau:\n\n```lua\nlocal obliterator = Janitor.new()\nobliterator:Add<PERSON>romise(Promise.delay(3)):and<PERSON><PERSON>Call(print, \"Finished!\"):catch(warn)\ntask.wait(1)\nobliterator:Cleanup()\n```\n\n### TypeScript:\n\n```ts\nimport { Jan<PERSON> } from \"@rbxts/janitor\";\n\nconst obliterator = new Janitor();\nobliterator.AddPromise(Promise.delay(3)).andThenCall(print, \"Finished!\").catch(warn);\ntask.wait(1);\nobliterator.Cleanup();\n```", "params": [{"name": "promiseObject", "desc": "The promise you want to add to the Janitor.", "lua_type": "Promise"}, {"name": "index?", "desc": "The index that can be used to clean up the object manually.", "lua_type": "unknown"}], "returns": [{"desc": "", "lua_type": "Promise"}], "function_type": "method", "errors": [{"lua_type": "NotAPromiseError", "desc": "Thrown if the promise is not a Promise."}], "source": {"line": 489, "path": "src/init.luau"}}, {"name": "Remove", "desc": "Cleans up whatever `Object` was set to this namespace by the 3rd parameter of [Janitor.Add](#Add).\n\n### Luau:\n\n```lua\nlocal obliterator = Janitor.new()\nobliterator:Add(workspace.Baseplate, \"Destroy\", \"Baseplate\")\nobliterator:Remove(\"Baseplate\")\n```\n\n### TypeScript:\n\n```ts\nimport { Workspace } from \"@rbxts/services\";\nimport { Janitor } from \"@rbxts/janitor\";\n\nconst obliterator = new Janitor<{ Baseplate: Part }>();\nobliterator.Add(Workspace.FindFirstChild(\"Baseplate\") as Part, \"Destroy\", \"Baseplate\");\nobliterator.Remove(\"Baseplate\");\n```", "params": [{"name": "index", "desc": "The index you want to remove.", "lua_type": "unknown"}], "returns": [{"desc": "", "lua_type": "<PERSON><PERSON>"}], "function_type": "method", "source": {"line": 554, "path": "src/init.luau"}}, {"name": "RemoveNoClean", "desc": "Removes an object from the Janitor without running a cleanup.\n\n### Luau\n\n```lua\nlocal obliterator = Janitor.new()\nobliterator:Add(function()\n\tprint(\"Removed!\")\nend, true, \"Function\")\n\nobliterator:RemoveNo<PERSON>lean(\"Function\") -- Does not print.\n```\n\n### TypeScript:\n\n```ts\nimport { Janitor } from \"@rbxts/janitor\";\n\nconst obliterator = new Janitor<{ Function: () => void }>();\nobliterator.Add(() => print(\"Removed!\"), true, \"Function\");\n\nobliterator.RemoveNoClean(\"Function\"); // Does not print.\n```", "params": [{"name": "index", "desc": "The index you are removing.", "lua_type": "unknown"}], "returns": [{"desc": "", "lua_type": "<PERSON><PERSON>"}], "function_type": "method", "since": "v1.15.0", "source": {"line": 588, "path": "src/init.luau"}}, {"name": "RemoveList", "desc": "Cleans up multiple objects at once.\n\n### Luau:\n\n```lua\nlocal obliterator = Janitor.new()\nobliterator:Add(function()\n\tprint(\"Removed One\")\nend, true, \"One\")\n\nobliterator:Add(function()\n\tprint(\"Removed Two\")\nend, true, \"Two\")\n\nobliterator:Add(function()\n\tprint(\"Removed Three\")\nend, true, \"Three\")\n\nobliterator:RemoveList(\"One\", \"Two\", \"Three\") -- Prints \"Removed One\", \"Removed Two\", and \"Removed Three\"\n```\n\n### TypeScript:\n\n```ts\nimport { Janitor } from \"@rbxts/janitor\";\n\ntype NoOp = () => void\n\nconst obliterator = new Janitor<{ One: NoOp, Two: NoOp, Three: NoOp }>();\nobliterator.Add(() => print(\"Removed One\"), true, \"One\");\nobliterator.Add(() => print(\"Removed Two\"), true, \"Two\");\nobliterator.Add(() => print(\"Removed Three\"), true, \"Three\");\n\nobliterator.RemoveList(\"One\", \"Two\", \"Three\"); // Prints \"Removed One\", \"Removed Two\", and \"Removed Three\"\n```", "params": [{"name": "...", "desc": "The indices you want to remove.", "lua_type": "unknown"}], "returns": [{"desc": "", "lua_type": "<PERSON><PERSON>"}], "function_type": "method", "since": "v1.14.0", "source": {"line": 643, "path": "src/init.luau"}}, {"name": "RemoveListNoClean", "desc": "Cleans up multiple objects at once without running their cleanup.\n\n### Luau:\n\n```lua\nlocal obliterator = Janitor.new()\nobliterator:Add(function()\n\tprint(\"Removed One\")\nend, true, \"One\")\n\nobliterator:Add(function()\n\tprint(\"Removed Two\")\nend, true, \"Two\")\n\nobliterator:Add(function()\n\tprint(\"Removed Three\")\nend, true, \"Three\")\n\nobliterator:Remove<PERSON>ist<PERSON>o<PERSON><PERSON>(\"One\", \"Two\", \"Three\") -- Nothing is printed.\n```\n\n### TypeScript:\n\n```ts\nimport { Janitor } from \"@rbxts/janitor\";\n\ntype NoOperation = () => void\n\nconst obliterator = new Janitor<{ One: NoOperation, Two: NoOperation, Three: NoOperation }>();\nobliterator.Add(() => print(\"Removed One\"), true, \"One\");\nobliterator.Add(() => print(\"Removed Two\"), true, \"Two\");\nobliterator.Add(() => print(\"Removed Three\"), true, \"Three\");\n\nobliterator.RemoveListNoClean(\"One\", \"Two\", \"Three\"); // Nothing is printed.\n```", "params": [{"name": "...", "desc": "The indices you want to remove.", "lua_type": "unknown"}], "returns": [{"desc": "", "lua_type": "<PERSON><PERSON>"}], "function_type": "method", "since": "v1.15.0", "source": {"line": 714, "path": "src/init.luau"}}, {"name": "GetAll", "desc": "Returns a frozen copy of the Janitor's indices.\n\n### Luau:\n\n```lua\nlocal obliterator = Jan<PERSON>.new()\nobliterator:Add(Workspace.Baseplate, \"Destroy\", \"Baseplate\")\nprint(obliterator:GetAll().Baseplate) -- Prints Baseplate.\n```\n\n### TypeScript:\n\n```ts\nimport { Workspace } from \"@rbxts/services\";\nimport { Janitor } from \"@rbxts/janitor\";\n\nconst obliterator = new Janitor<{ Baseplate: Part }>();\nobliterator.Add(Workspace.FindFirstChild(\"Baseplate\") as Part, \"Destroy\", \"Baseplate\");\nprint(obliterator.GetAll().Baseplate); // Prints Baseplate.\n```", "params": [], "returns": [{"desc": "", "lua_type": "{[any]: any}"}], "function_type": "method", "since": "v1.15.1", "source": {"line": 799, "path": "src/init.luau"}}, {"name": "Cleanup", "desc": "Calls each object's `methodName` (or calls the object if\n`methodName == true`) and removes them from the Janitor. Also clears the\nnamespace. This function is also called when you call a Janitor object (so\nit can be used as a destructor callback).\n\n### Luau:\n\n```lua\nobliterator:Cleanup() -- Valid.\nobliterator() -- Also valid.\n```\n\n### TypeScript:\n\n```ts\nobliterator.Cleanup()\n// TypeScript version doesn't support the __call method of cleaning.\n```", "params": [], "returns": [], "function_type": "method", "source": {"line": 827, "path": "src/init.luau"}}, {"name": "Destroy", "desc": "Calls [Janitor.Cleanup](#Cleanup) and renders the Janitor unusable.\n\n:::warning Metatable Removal\nRunning this will make any further attempts to call a method of Janitor\nerror.\n:::", "params": [], "returns": [], "function_type": "method", "source": {"line": 907, "path": "src/init.luau"}}, {"name": "LinkToInstance", "desc": "\"Links\" this Janitor to an Instance, such that the Janitor will `Cleanup`\nwhen the Instance is `Destroy()`d and garbage collected. A Janitor may only\nbe linked to one instance at a time, unless `allowMultiple` is true. When\ncalled with a truthy `allowMultiple` parameter, the Janitor will \"link\" the\nInstance without overwriting any previous links, and will also not be\noverwritable. When called with a falsy `allowMultiple` parameter, the\nJanitor will overwrite the previous link which was also called with a falsy\n`allowMultiple` parameter, if applicable.\n\n### Luau:\n\n```lua\nlocal obliterator = Janitor.new()\n\nobliterator:Add(function()\n\tprint(\"Cleaning up!\")\nend, true)\n\ndo\n\tlocal folder = Instance.new(\"Folder\")\n\tobliterator:LinkToInstance(folder)\n\tfolder:Destroy()\nend\n```\n\n### TypeScript:\n\n```ts\nimport { Janitor } from \"@rbxts/janitor\";\n\nconst obliterator = new Janitor();\nobliterator.Add(() => print(\"Cleaning up!\"), true);\n\n{\n\tconst folder = new Instance(\"Folder\");\n\tobliterator.LinkToInstance(folder, false);\n\tfolder.Destroy();\n}\n```", "params": [{"name": "object", "desc": "The instance you want to link the <PERSON><PERSON> to.", "lua_type": "Instance"}, {"name": "allowMultiple?", "desc": "Whether or not to allow multiple links on the same Janitor.", "lua_type": "boolean"}], "returns": [{"desc": "A RBXScriptConnection that can be disconnected to prevent the cleanup of LinkToInstance.", "lua_type": "RBXScriptConnection"}], "function_type": "method", "source": {"line": 971, "path": "src/init.luau"}}, {"name": "LinkToInstances", "desc": "Links several instances to a new Janitor, which is then returned.", "params": [{"name": "...", "desc": "All the Instances you want linked.", "lua_type": "Instance"}], "returns": [{"desc": "A new Janitor that can be used to manually disconnect all LinkToInstances.", "lua_type": "<PERSON><PERSON>"}], "function_type": "method", "source": {"line": 981, "path": "src/init.luau"}}], "properties": [{"name": "CurrentlyCleaning", "desc": "Whether or not the Jan<PERSON> is currently cleaning up.", "lua_type": "boolean", "readonly": true, "source": {"line": 134, "path": "src/init.luau"}}, {"name": "SuppressInstanceReDestroy", "desc": "Whether or not you want to suppress the re-destroying of instances. Default\nis false, which is the original behavior.", "lua_type": "boolean", "since": "1.15.4", "source": {"line": 142, "path": "src/init.luau"}}, {"name": "UnsafeThreadCleanup", "desc": "Whether or not to use the unsafe fast defer function for cleaning up\nthreads. This might be able to throw, so be careful. If you're getting any\nthread related errors, chances are it is this.", "lua_type": "boolean", "since": "1.18.0", "source": {"line": 151, "path": "src/init.luau"}}], "types": [], "name": "<PERSON><PERSON>", "desc": "Janitor is a light-weight, flexible object for cleaning up connections,\ninstances, or anything. This implementation covers all use cases, as it\ndoesn't force you to rely on naive typechecking to guess how an instance\nshould be cleaned up. Instead, the developer may specify any behavior for\nany object.\n\nThis is the fastest OOP library on Roblox of its kind on both X86-64 as\nwell as ARM64.", "source": {"line": 118, "path": "src/init.luau"}}