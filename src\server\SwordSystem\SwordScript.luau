local Players = game:GetService("Players")
local Debris = game:GetService("Debris")
local RunService = game:GetService("RunService")

local CombatHandler = require(script.Parent.CombatHandler)

local tool = script.Parent
local handle = tool:WaitFor<PERSON>hild("Handle")
local hitbox = tool:WaitForChild("Hitbox")

local pointsRequired = tool:WaitForChild("PointsRequired").Value
local hitboxScale = tool:WaitForChild("HitboxScale").Value

local sounds = {
	Slash = handle:WaitForChild("SwordSlash"),
	Lunge = handle:WaitForChild("SwordLunge"),
	Unsheath = handle:WaitFor<PERSON>hild("Unsheath")
}

local grips = {
	Up = CFrame.new(0, 0, -1.7, 0, 0, 1, 1, 0, 0, 0, 1, 0),
	Out = CFrame.new(0, 0, -1.7, 0, 1, 0, 1, 0, 0, 0, 0, -1)
}

local toolEquipped = false
local lastAttack = 0
local character = nil
local player = nil
local humanoid = nil

tool.Grip = grips.Up
tool.Enabled = true

local function checkIfAlive()
	return character and character.Parent and humanoid and humanoid.Parent and humanoid.Health > 0
end

local function onHit(hit: BasePart)
	if not hit or not hit.Parent or not checkIfAlive() or not toolEquipped then
		return
	end

	local hitCharacter = hit.Parent
	if hitCharacter == character then
		return
	end

	local hitHumanoid = hitCharacter:FindFirstChildOfClass("Humanoid")
	if not hitHumanoid or hitHumanoid.Health <= 0 then
		return
	end

	local hitPlayer = Players:GetPlayerFromCharacter(hitCharacter)
	if not hitPlayer then
		return
	end

	CombatHandler.handleSwordHit(player, hit, tostring(pointsRequired))
end

local function attack()
	sounds.Slash:Play()

	if humanoid and humanoid.RigType == Enum.HumanoidRigType.R15 then
		local slashAnim = tool:FindFirstChild("R15Slash")
		if slashAnim then
			local track = humanoid:LoadAnimation(slashAnim)
			track:Play(0)
		end
	end
end

local function lunge()
	sounds.Lunge:Play()

	if humanoid and humanoid.RigType == Enum.HumanoidRigType.R15 then
		local lungeAnim = tool:FindFirstChild("R15Lunge")
		if lungeAnim then
			local track = humanoid:LoadAnimation(lungeAnim)
			track:Play(0)
		end
	end

	task.wait(0.2)
	tool.Grip = grips.Out
	task.wait(0.6)
	tool.Grip = grips.Up
end

local function onActivated()
	if not tool.Enabled or not toolEquipped or not checkIfAlive() then
		return
	end

	tool.Enabled = false
	local currentTick = RunService.Stepped:Wait()

	if currentTick - lastAttack < 0.2 then
		lunge()
	else
		attack()
	end

	lastAttack = currentTick
	tool.Enabled = true
end

local function onEquipped()
	character = tool.Parent
	player = Players:GetPlayerFromCharacter(character)
	humanoid = character and character:FindFirstChildOfClass("Humanoid")

	if not checkIfAlive() then
		return
	end

	toolEquipped = true
	sounds.Unsheath:Play()
end

local function onUnequipped()
	tool.Grip = grips.Up
	toolEquipped = false
	character = nil
	player = nil
	humanoid = nil
end

tool.Activated:Connect(onActivated)
tool.Equipped:Connect(onEquipped)
tool.Unequipped:Connect(onUnequipped)

hitbox.Touched:Connect(onHit)
