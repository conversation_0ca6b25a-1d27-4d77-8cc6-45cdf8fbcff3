{"name": "ShapecastHitbox", "className": "ModuleScript", "filePaths": ["src\\init.luau", "default.project.json"], "children": [{"name": "Hitbox", "className": "ModuleScript", "filePaths": ["src\\Hitbox.luau"]}, {"name": "Settings", "className": "ModuleScript", "filePaths": ["src\\Settings.luau"]}, {"name": "<PERSON><PERSON>", "className": "ModuleScript", "filePaths": ["src\\Solvers\\init.luau"], "children": [{"name": "Attachment", "className": "ModuleScript", "filePaths": ["src\\Solvers\\Attachment.luau"]}, {"name": "Blockcast", "className": "ModuleScript", "filePaths": ["src\\Solvers\\Blockcast.luau"]}, {"name": "Raycast", "className": "ModuleScript", "filePaths": ["src\\Solvers\\Raycast.luau"]}, {"name": "Spherecast", "className": "ModuleScript", "filePaths": ["src\\Solvers\\Spherecast.luau"]}]}, {"name": "Types", "className": "ModuleScript", "filePaths": ["src\\Types.luau"]}, {"name": "Visualizers", "className": "ModuleScript", "filePaths": ["src\\Visualizers\\init.luau"], "children": [{"name": "Blockcast", "className": "ModuleScript", "filePaths": ["src\\Visualizers\\Blockcast.luau"]}, {"name": "Raycast", "className": "ModuleScript", "filePaths": ["src\\Visualizers\\Raycast.luau"]}, {"name": "Spherecast", "className": "ModuleScript", "filePaths": ["src\\Visualizers\\Spherecast.luau"]}]}]}