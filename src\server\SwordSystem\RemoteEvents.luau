local ReplicatedStorage = game:GetService("ReplicatedStorage")

local RemoteEvents = {}

local function createRemotesFolder()
	local remotesFolder = ReplicatedStorage:FindFirstChild("Remotes")
	if not remotesFolder then
		remotesFolder = Instance.new("Folder")
		remotesFolder.Name = "Remotes"
		remotesFolder.Parent = ReplicatedStorage
	end
	return remotesFolder
end

function RemoteEvents.initialize()
	local remotesFolder = createRemotesFolder()
	
	local swordHitRemote = Instance.new("RemoteEvent")
	swordHitRemote.Name = "SwordHit"
	swordHitRemote.Parent = remotesFolder
	
	return {
		SwordHit = swordHitRemote,
	}
end

return RemoteEvents
