local Font = import("../Enum/Font")
local Instance = import("../Instance")
local TextTruncate = import("../Enum/TextTruncate")
local TextXAlignment = import("../Enum/TextXAlignment")
local TextYAlignment = import("../Enum/TextYAlignment")
local typeof = import("../functions/typeof")

describe("instances.TextLabel", function()
	it("should instantiate", function()
		local instance = Instance.new("TextLabel")

		assert.not_nil(instance)
	end)

	it("should have properties defined", function()
		local instance = Instance.new("TextLabel")
		assert.equal(typeof(instance.Font), "EnumItem")
		assert.equal(instance.Font.EnumType, Font)
		assert.equal(typeof(instance.Text), "string")
		assert.equal(typeof(instance.TextColor3), "Color3")
		assert.equal(typeof(instance.TextSize), "number")
		assert.equal(typeof(instance.TextWrapped), "boolean")
		assert.equal(typeof(instance.TextTruncate), "EnumItem")
		assert.equal(instance.TextTruncate.EnumType, TextTruncate)
		assert.equal(typeof(instance.TextXAlignment), "EnumItem")
		assert.equal(instance.TextXAlignment.EnumType, TextXAlignment)
		assert.equal(typeof(instance.TextYAlignment), "EnumItem")
		assert.equal(instance.TextYAlignment.EnumType, TextYAlignment)
		assert.equal(typeof(instance.TextScaled), "boolean")
	end)
end)