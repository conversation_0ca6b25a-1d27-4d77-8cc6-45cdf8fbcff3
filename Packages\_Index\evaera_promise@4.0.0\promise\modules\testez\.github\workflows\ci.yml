name: CI

on:
  pull_request:
  push:
    branches:
      - master

jobs:
  lib:
    name: TestEZ Library Tests
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v1
      with:
        submodules: true

    - uses: leafo/gh-actions-lua@v5
      with:
        luaVersion: "5.1"

    - uses: leafo/gh-actions-luarocks@v2

    - name: Install dependencies
      run: |
        luarocks install luafilesystem
        luarocks install luacov
        luarocks install luacov-coveralls --server=http://rocks.moonscript.org/dev
        luarocks install luacheck

    - name: Test
      run: |
        lua -lluacov test/lemur.lua
        luacheck src tests

    # luacov-coveralls default settings do not function on GitHub Actions.
    # We need to pass different service name and repo token explicitly
    - name: Report to Coveralls
      run: luacov-coveralls --repo-token $REPO_TOKEN --service-name github
      env:
        REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  cli:
    name: TestEZ CLI Tests
    runs-on: ubuntu-latest

    strategy:
      matrix:
        rust_version: [stable, "1.40.0"]

    steps:
    - uses: actions/checkout@v1
      with:
        submodules: true

    - name: Setup Rust toolchain
      run: rustup default ${{ matrix.rust_version }}

    - uses: rojo-rbx/setup-foreman@v1
      with:
        version: "^0.6.0"
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Report tool versions
      run: rojo --version

    - name: Build
      run: cargo build --locked --verbose
      env:
        RUST_BACKTRACE: 1

    - name: Run tests
      run: cargo test --locked --verbose

    - name: Rustfmt and Clippy
      run: |
        cargo fmt -- --check
        cargo clippy
      if: matrix.rust_version == 'stable'