local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

local DataStore = require(script.DataStore)
local SwordSystem = require(script.SwordSystem)

local function onPlayerAdded(player: Player)
	local success = DataStore.loadPlayerProfileAsync(player)
	if success then
		local swordManager = SwordSystem.getSwordManager()
		swordManager.setupPlayer(player)
	end
end

local function onPlayerRemoving(player: Player)
	local swordManager = SwordSystem.getSwordManager()
	swordManager.cleanupPlayer(player)
	DataStore.releasePlayerProfile(player)
end

SwordSystem.initialize()

local swordManager = SwordSystem.getSwordManager()
swordManager.setPointsProvider(function(player: Player): number
	return DataStore.getPoints(player)
end)

SwordSystem.setDataStoreProvider(DataStore)

Players.PlayerAdded:Connect(onPlayerAdded)
Players.PlayerRemoving:Connect(onPlayerRemoving)

for _, player in Players:GetPlayers() do
	task.spawn(onPlayerAdded, player)
end

if RunService:IsStudio() then
	game:BindToClose(function()
		DataStore.shutdown()
	end)
end