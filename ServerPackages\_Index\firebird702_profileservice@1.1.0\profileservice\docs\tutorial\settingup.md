ProfileService is supposed to be a ModuleScript which you can place inside your Roblox game's *ServerScriptService* or wherever else is preferred. ProfileService can only be used server-side

### Option #1: Get ProfileService from the Roblox library

   - Get the library model [here](https://www.roblox.com/library/5331689994/ProfileService)
   - Move "ProfileService" ModuleScript to ServerScriptService:

![Open toolbox menu](../images/Toolbox1.png)
![Find the ProfileService model](../images/Toolbox2.png)
![Move ProfileService to ServerScriptService](../images/Toolbox3.png)

### Option #2: Github
* [ProfileService repository](https://github.com/MadStudioRoblox/ProfileService)
