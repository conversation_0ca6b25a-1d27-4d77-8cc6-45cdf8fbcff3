
local ProfileManager = require(script.Parent.ProfileManager)
local ProfileTypes = require(game:GetService("ReplicatedStorage").Shared.Types.ProfileTypes)

type PlayerData = ProfileTypes.PlayerData

local LeaderstatsManager = {}

local SessionPoints: { [Player]: number } = {}

local function createLeaderstats(player: Player): Folder
	local leaderstats = Instance.new("Folder")
	leaderstats.Name = "leaderstats"
	leaderstats.Parent = player
	
	local points = Instance.new("IntValue")
	points.Name = "Points"
	points.Value = 0
	points.Parent = leaderstats
	
	local totalPoints = Instance.new("IntValue")
	totalPoints.Name = "Total Points"
	totalPoints.Value = 0
	totalPoints.Parent = leaderstats
	
	return leaderstats
end

local function updateLeaderstats(player: Player)
	local leaderstats = player:FindFirstChild("leaderstats")
	if not leaderstats then
		return
	end

	local profile = ProfileManager.getProfile(player)
	if not profile then
		return
	end

	local pointsValue = leaderstats:FindFirstChild("Points") :: IntValue?
	local totalPointsValue = leaderstats:FindFirstChild("Total Points") :: IntValue?

	if pointsValue then
		pointsValue.Value = SessionPoints[player] or 0
	end

	if totalPointsValue then
		totalPointsValue.Value = profile.Data.TotalPoints
	end
end

function LeaderstatsManager.setupPlayer(player: Player)
	createLeaderstats(player)
	SessionPoints[player] = 0
	updateLeaderstats(player)
end

function LeaderstatsManager.addPoints(player: Player, amount: number)
	SessionPoints[player] = (SessionPoints[player] or 0) + amount
	updateLeaderstats(player)
end

function LeaderstatsManager.getPoints(player: Player): number
	return SessionPoints[player] or 0
end

function LeaderstatsManager.convertPointsToTotalPoints(player: Player): number
	local points = SessionPoints[player] or 0
	if points > 0 then
		local profile = ProfileManager.getProfile(player)
		if profile and profile:IsActive() then
			profile.Data.TotalPoints += points
		end
		SessionPoints[player] = 0
		updateLeaderstats(player)
	end
	return points
end

function LeaderstatsManager.updateDisplay(player: Player)
	updateLeaderstats(player)
end

function LeaderstatsManager.cleanupPlayer(player: Player)
	SessionPoints[player] = nil
end

return LeaderstatsManager
