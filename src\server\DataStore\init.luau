
local ProfileManager = require(script.ProfileManager)
local LeaderstatsManager = require(script.LeaderstatsManager)
local ProfileTypes = require(game:GetService("ReplicatedStorage").Shared.Types.ProfileTypes)

type Profile = ProfileTypes.Profile
type PlayerData = ProfileTypes.PlayerData

local DataStore = {}

function DataStore.loadPlayerProfileAsync(player: Player): boolean
	local profile = ProfileManager.loadProfileAsync(player)

	if profile == nil then
		player:Kick("Failed to load profile - please rejoin")
		return false
	end

	LeaderstatsManager.setupPlayer(player)
	return true
end

function DataStore.getPlayerProfile(player: Player): Profile?
	return ProfileManager.getProfile(player)
end

function DataStore.getPlayerData(player: Player): PlayerData?
	local profile = ProfileManager.getProfile(player)
	return profile and profile.Data
end

function DataStore.releasePlayerProfile(player: Player)
	LeaderstatsManager.cleanupPlayer(player)
	ProfileManager.releaseProfile(player)
end

function DataStore.updatePlayerData(player: Player, updateFunction: (data: PlayerData) -> ())
	local profile = ProfileManager.getProfile(player)
	if profile and profile:IsActive() then
		updateFunction(profile.Data)
	end
end

function DataStore.addPoints(player: Player, amount: number)
	LeaderstatsManager.addPoints(player, amount)
end

function DataStore.getPoints(player: Player): number
	return LeaderstatsManager.getPoints(player)
end

function DataStore.convertPointsToTotalPoints(player: Player): number
	local convertedPoints = LeaderstatsManager.convertPointsToTotalPoints(player)
	LeaderstatsManager.updateDisplay(player)
	return convertedPoints
end

function DataStore.addTotalPoints(player: Player, amount: number)
	DataStore.updatePlayerData(player, function(data)
		data.TotalPoints += amount
	end)
	LeaderstatsManager.updateDisplay(player)
end

function DataStore.addGold(player: Player, amount: number)
	DataStore.updatePlayerData(player, function(data)
		data.Gold += amount
	end)
end

function DataStore.removeGold(player: Player, amount: number): boolean
	local profile = ProfileManager.getProfile(player)
	if profile and profile:IsActive() and profile.Data.Gold >= amount then
		profile.Data.Gold -= amount
		return true
	end
	return false
end

function DataStore.addGems(player: Player, amount: number)
	DataStore.updatePlayerData(player, function(data)
		data.Gems += amount
	end)
end

function DataStore.removeGems(player: Player, amount: number): boolean
	local profile = ProfileManager.getProfile(player)
	if profile and profile:IsActive() and profile.Data.Gems >= amount then
		profile.Data.Gems -= amount
		return true
	end
	return false
end

function DataStore.addExperience(player: Player, amount: number)
	DataStore.updatePlayerData(player, function(data)
		data.Experience += amount
		local newLevel = math.floor(data.Experience / 1000) + 1
		if newLevel > data.Level then
			data.Level = newLevel
		end
	end)
end

function DataStore.addItem(player: Player, itemName: string, amount: number?)
	local actualAmount = amount or 1
	DataStore.updatePlayerData(player, function(data)
		data.Inventory.Items[itemName] = (data.Inventory.Items[itemName] or 0) + actualAmount
	end)
end

function DataStore.removeItem(player: Player, itemName: string, amount: number?): boolean
	local actualAmount = amount or 1
	local profile = ProfileManager.getProfile(player)
	if profile and profile:IsActive() then
		local currentAmount = profile.Data.Inventory.Items[itemName] or 0
		if currentAmount >= actualAmount then
			profile.Data.Inventory.Items[itemName] = currentAmount - actualAmount
			if profile.Data.Inventory.Items[itemName] <= 0 then
				profile.Data.Inventory.Items[itemName] = nil
			end
			return true
		end
	end
	return false
end

function DataStore.equipItem(player: Player, itemType: "Weapon" | "Armor" | "Accessory", itemName: string?)
	DataStore.updatePlayerData(player, function(data)
		data.Inventory.Equipment[itemType] = itemName
	end)
end

function DataStore.unlockAchievement(player: Player, achievementId: string)
	DataStore.updatePlayerData(player, function(data)
		data.Achievements[achievementId] = true
	end)
end

function DataStore.addGamePlayed(player: Player)
	DataStore.updatePlayerData(player, function(data)
		data.Stats.GamesPlayed += 1
	end)
end

function DataStore.addGameWon(player: Player)
	DataStore.updatePlayerData(player, function(data)
		data.Stats.GamesWon += 1
	end)
end

function DataStore.addDeath(player: Player)
	DataStore.updatePlayerData(player, function(data)
		data.Stats.Deaths += 1
	end)
end

function DataStore.addKill(player: Player)
	DataStore.updatePlayerData(player, function(data)
		data.Stats.Kills += 1
	end)
end

function DataStore.addOrbsCollected(player: Player, amount: number?)
	local actualAmount = amount or 1
	DataStore.updatePlayerData(player, function(data)
		data.Stats.OrbsCollected += actualAmount
	end)
end

function DataStore.getPlayerStats(player: Player)
	local playerData = DataStore.getPlayerData(player)
	return playerData and playerData.Stats
end

function DataStore.updateSettings(player: Player, settings: { [string]: any })
	DataStore.updatePlayerData(player, function(data)
		for key, value in settings do
			data.Settings[key] = value
		end
	end)
end

function DataStore.addFriend(player: Player, friendUserId: number)
	DataStore.updatePlayerData(player, function(data)
		data.Friends[friendUserId] = true
	end)
end

function DataStore.removeFriend(player: Player, friendUserId: number)
	DataStore.updatePlayerData(player, function(data)
		data.Friends[friendUserId] = nil
	end)
end

function DataStore.sendGlobalUpdate(targetUserId: number, updateType: string, updateData: { [string]: any })
	ProfileManager.sendGlobalUpdate(targetUserId, {
		Type = updateType,
		Data = updateData,
	})
end

function DataStore.sendTotalPointsGift(targetUserId: number, amount: number)
	ProfileManager.sendGlobalUpdate(targetUserId, {
		Type = "TotalPoints",
		Amount = amount,
	})
end

function DataStore.sendGoldGift(targetUserId: number, amount: number)
	ProfileManager.sendGlobalUpdate(targetUserId, {
		Type = "Gold",
		Amount = amount,
	})
end

function DataStore.sendGemsGift(targetUserId: number, amount: number)
	ProfileManager.sendGlobalUpdate(targetUserId, {
		Type = "Gems",
		Amount = amount,
	})
end

function DataStore.sendItemGift(targetUserId: number, itemName: string, amount: number?)
	ProfileManager.sendGlobalUpdate(targetUserId, {
		Type = "Item",
		ItemName = itemName,
		Amount = amount or 1,
	})
end

function DataStore.getAllLoadedProfiles(): { [Player]: Profile }
	return ProfileManager.getAllProfiles()
end

function DataStore.shutdown()
	ProfileManager.shutdown()
end

return DataStore
